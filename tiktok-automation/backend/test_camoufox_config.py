#!/usr/bin/env python3
"""
Test script to validate Camoufox configuration
"""

import asyncio
import json
from camoufox_integration.antidetect_config import AntidetectConfigGenerator
from camoufox_integration.camoufox_wrapper import local_camoufox

async def test_config_generation():
    """Test configuration generation without unsupported properties"""
    
    print("Testing Camoufox configuration generation...")
    
    # Create antidetect config generator
    config_generator = AntidetectConfigGenerator()
    
    # Test base config
    base_config = {
        "navigator.userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
        "screen.width": 1920,
        "screen.height": 1080,
        "timezone": "America/New_York"
    }
    
    # Generate enhanced config
    enhanced_config = config_generator.get_enhanced_config(base_config)
    
    print(f"Generated config keys: {list(enhanced_config.keys())}")
    
    # Check for unsupported properties
    unsupported_properties = {
        'dns.servers',
        'dns.leak.protection', 
        'dns.over.https',
        'navigator.deviceMemory',
        'webdriver.detection.evasion',
        'automation.detection.evasion',
        'canvas.fingerprinting.protection',
        'audio.fingerprinting.protection',
        'font.fingerprinting.protection',
        'webgl.fingerprinting.protection',
        'webrtc.leak.protection',
        'performance.timing.randomize',
        'mouse.movement.natural',
        'keyboard.typing.natural',
        'scroll.behavior.smooth'
    }
    
    found_unsupported = []
    for key in enhanced_config.keys():
        if key in unsupported_properties:
            found_unsupported.append(key)
    
    if found_unsupported:
        print(f"❌ Found unsupported properties: {found_unsupported}")
        return False
    else:
        print("✅ No unsupported properties found in enhanced config")
    
    # Test config preparation
    prepared_config = local_camoufox._prepare_optimized_config(enhanced_config)
    
    print(f"Prepared config keys: {list(prepared_config.keys())}")
    
    # Check prepared config for unsupported properties
    found_unsupported_prepared = []
    for key in prepared_config.keys():
        if key in unsupported_properties:
            found_unsupported_prepared.append(key)
    
    if found_unsupported_prepared:
        print(f"❌ Found unsupported properties in prepared config: {found_unsupported_prepared}")
        return False
    else:
        print("✅ No unsupported properties found in prepared config")
    
    # Test config validation
    try:
        local_camoufox._validate_config(prepared_config.copy())
        print("✅ Config validation passed")
    except Exception as e:
        print(f"❌ Config validation failed: {e}")
        return False
    
    print("\n📋 Final config summary:")
    print(json.dumps(prepared_config, indent=2, default=str))
    
    return True

async def main():
    """Main test function"""
    
    print("🧪 Testing Camoufox Configuration")
    print("=" * 50)
    
    try:
        success = await test_config_generation()
        
        if success:
            print("\n✅ All tests passed! Camoufox configuration should work correctly.")
        else:
            print("\n❌ Some tests failed. Please check the configuration.")
            
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
Simple test to check for unsupported properties in config
"""

def test_config_properties():
    """Test for unsupported properties"""
    
    print("🧪 Testing for unsupported Camoufox properties")
    print("=" * 50)
    
    # List of known unsupported properties that cause "Unknown property" errors
    unsupported_properties = {
        'dns.servers',
        'dns.leak.protection', 
        'dns.over.https',
        'navigator.deviceMemory',
        'webdriver.detection.evasion',
        'automation.detection.evasion',
        'canvas.fingerprinting.protection',
        'canvas.noise.enabled',
        'canvas.noise.amount',
        'audio.fingerprinting.protection',
        'audio.context.noise',
        'font.fingerprinting.protection',
        'font.metrics.randomization',
        'webgl.fingerprinting.protection',
        'webgl.noise.enabled',
        'webrtc.leak.protection',
        'performance.timing.randomize',
        'mouse.movement.natural',
        'mouse.movement.variance',
        'mouse.click.delay',
        'keyboard.typing.natural',
        'keyboard.typing.speed',
        'keyboard.typing.variance',
        'scroll.behavior.smooth',
        'scroll.speed.variance',
    }
    
    # Sample config that might be generated
    sample_config = {
        "navigator.userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
        "screen.width": 1920,
        "screen.height": 1080,
        "timezone": "America/New_York",
        "webrtc:ipv4": "*************",
        "geolocation:latitude": 40.7128,
        "geolocation:longitude": -74.0060,
        "prefs:network.trr.mode": 2,
        "prefs:network.trr.uri": "https://cloudflare-dns.com/dns-query",
        "prefs:network.trr.bootstrapAddress": "*******",
    }
    
    print("✅ Sample config properties:")
    for key in sorted(sample_config.keys()):
        print(f"  - {key}")
    
    # Check for unsupported properties
    found_unsupported = []
    for key in sample_config.keys():
        if key in unsupported_properties:
            found_unsupported.append(key)
    
    if found_unsupported:
        print(f"\n❌ Found unsupported properties: {found_unsupported}")
        return False
    else:
        print("\n✅ No unsupported properties found!")
    
    print("\n📋 Supported property patterns:")
    print("  - navigator.* (user agent, platform, etc.)")
    print("  - screen.* (width, height, colorDepth, etc.)")
    print("  - window.* (innerWidth, innerHeight, etc.)")
    print("  - webrtc:* (ipv4, ipv6)")
    print("  - geolocation:* (latitude, longitude, accuracy)")
    print("  - prefs:* (Firefox preferences)")
    print("  - timezone (timezone string)")
    print("  - locale (locale string)")
    print("  - fonts (array of font names)")
    print("  - webgl (vendor, renderer)")
    
    print("\n🚫 Unsupported property patterns:")
    print("  - dns.* (use prefs:network.* instead)")
    print("  - *.protection, *.evasion (antidetect features)")
    print("  - mouse.*, keyboard.*, scroll.* (behavioral features)")
    print("  - *.noise.* (noise injection features)")
    
    return True

def main():
    """Main test function"""
    
    try:
        success = test_config_properties()
        
        if success:
            print("\n✅ Configuration test passed!")
            print("The current config should work with Camoufox without 'Unknown property' errors.")
        else:
            print("\n❌ Configuration test failed!")
            print("Please remove unsupported properties from the config.")
            
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")

if __name__ == "__main__":
    main()

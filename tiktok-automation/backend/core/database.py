"""
Database configuration and session management
"""

import asyncio
from typing import Async<PERSON>enerator

from sqlalchemy import create_engine, MetaD<PERSON>
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from loguru import logger

from .config import settings

# Create database engines
if settings.DATABASE_URL.startswith("sqlite"):
    # SQLite configuration
    SQLALCHEMY_DATABASE_URL = settings.DATABASE_URL
    ASYNC_SQLALCHEMY_DATABASE_URL = settings.DATABASE_URL.replace("sqlite:///", "sqlite+aiosqlite:///")
    
    engine = create_engine(
        SQLALCHEMY_DATABASE_URL,
        connect_args={"check_same_thread": False},
        echo=settings.DATABASE_ECHO,
        pool_pre_ping=True,
        pool_recycle=300,
    )
    
    async_engine = create_async_engine(
        ASYNC_SQLALCHEMY_DATABASE_URL,
        echo=settings.DATABASE_ECHO,
        pool_pre_ping=True,
        pool_recycle=300,
    )
else:
    # PostgreSQL configuration
    engine = create_engine(
        settings.DATABASE_URL,
        echo=settings.DATABASE_ECHO,
        pool_size=10,
        max_overflow=20,
        pool_pre_ping=True,
        pool_recycle=300,
    )
    
    async_engine = create_async_engine(
        settings.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://"),
        echo=settings.DATABASE_ECHO,
        pool_size=10,
        max_overflow=20,
        pool_pre_ping=True,
        pool_recycle=300,
    )

# Create session makers
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
AsyncSessionLocal = async_sessionmaker(
    async_engine, class_=AsyncSession, expire_on_commit=False
)

# Create declarative base
Base = declarative_base()

# Metadata for migrations
metadata = MetaData()


async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """Get async database session for dependency injection"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise


def get_async_session_context():
    """Get async database session context manager for direct usage"""
    return AsyncSessionLocal()


def get_session():
    """Get sync database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def init_db():
    """Initialize database tables"""
    try:
        # Import all models to ensure they are registered
        from models import (
            browser_profile,
            tiktok_account,
            proxy,
            competitor,
            follow_task,
            activity_log,
            user_settings
        )
        
        # Create all tables
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("Database tables created successfully")
        
        # Create default data if needed
        await create_default_data()
        
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise


async def create_default_data():
    """Create default data for the application"""
    async with AsyncSessionLocal() as session:
        try:
            # Import models
            from models.user_settings import UserSettings
            
            # Check if default settings exist
            default_settings = await session.get(UserSettings, 1)
            if not default_settings:
                default_settings = UserSettings(
                    id=1,
                    theme="dark",
                    language="en",
                    auto_save_cookies=True,
                    follow_limit_per_hour=50,
                    follow_limit_per_day=200,
                    delay_between_follows_min=2,
                    delay_between_follows_max=5,
                    enable_notifications=True,
                    minimize_to_tray=False,
                )
                session.add(default_settings)
                await session.commit()
                logger.info("Default settings created")
                
        except Exception as e:
            logger.error(f"Failed to create default data: {e}")
            await session.rollback()


async def close_db():
    """Close database connections"""
    await async_engine.dispose()
    engine.dispose()
    logger.info("Database connections closed")


# Database health check
async def check_db_health() -> bool:
    """Check database connection health"""
    try:
        from sqlalchemy import text
        async with AsyncSessionLocal() as session:
            await session.execute(text("SELECT 1"))
        return True
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return False

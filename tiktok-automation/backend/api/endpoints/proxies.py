"""
Proxy management API endpoints
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from fastapi import APIRouter, HTTPException, Query, Body
from pydantic import BaseModel, Field, field_serializer

from services.proxy_service import ProxyService
from models.proxy import Proxy, ProxyType, ProxyStatus

router = APIRouter()
proxy_service = ProxyService()


# Pydantic models for request/response
class ProxyCreateRequest(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    proxy_type: ProxyType
    host: str = Field(..., min_length=1, max_length=255)
    port: int = Field(..., ge=1, le=65535)
    username: Optional[str] = Field(None, max_length=255)
    password: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    ssh_private_key: Optional[str] = None
    ssh_passphrase: Optional[str] = Field(None, max_length=255)
    validate_on_create: bool = True


class ProxyUpdateRequest(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    host: Optional[str] = Field(None, min_length=1, max_length=255)
    port: Optional[int] = Field(None, ge=1, le=65535)
    username: Optional[str] = Field(None, max_length=255)
    password: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    is_active: Optional[bool] = None
    ssh_private_key: Optional[str] = None
    ssh_passphrase: Optional[str] = Field(None, max_length=255)


class ProxyResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    proxy_type: str
    host: str
    port: int
    username: Optional[str]
    status: str
    is_active: bool
    response_time_ms: Optional[int]
    success_rate: int
    total_requests: int
    failed_requests: int
    country: Optional[str]
    city: Optional[str]
    ip_address: Optional[str]
    last_checked: Optional[datetime]
    last_error: Optional[str]
    usage_count: int
    last_used: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    @field_serializer('last_checked', 'last_used', 'created_at', 'updated_at')
    def serialize_datetime(self, value: Optional[datetime]) -> Optional[str]:
        return value.isoformat() if value else None

    class Config:
        from_attributes = True


class ProxyImportRequest(BaseModel):
    proxies: List[Dict[str, Any]]
    validate_on_import: bool = False


@router.get("/", response_model=List[ProxyResponse])
async def get_proxies(
    active_only: bool = Query(False, description="Filter active proxies only"),
    proxy_type: Optional[ProxyType] = Query(None, description="Filter by proxy type"),
    status: Optional[ProxyStatus] = Query(None, description="Filter by status"),
    limit: Optional[int] = Query(None, ge=1, le=100, description="Limit number of results"),
    offset: int = Query(0, ge=0, description="Offset for pagination")
):
    """Get list of proxies"""
    try:
        proxies = await proxy_service.get_proxies(
            active_only=active_only,
            proxy_type=proxy_type,
            status=status,
            limit=limit,
            offset=offset
        )
        return [ProxyResponse.from_orm(proxy) for proxy in proxies]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{proxy_id}", response_model=ProxyResponse)
async def get_proxy(proxy_id: int):
    """Get proxy by ID"""
    try:
        proxy = await proxy_service.get_proxy(proxy_id)
        if not proxy:
            raise HTTPException(status_code=404, detail="Proxy not found")
        return ProxyResponse.from_orm(proxy)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/", response_model=ProxyResponse)
async def create_proxy(request: ProxyCreateRequest):
    """Create a new proxy"""
    try:
        proxy = await proxy_service.create_proxy(
            name=request.name,
            proxy_type=request.proxy_type,
            host=request.host,
            port=request.port,
            username=request.username,
            password=request.password,
            description=request.description,
            ssh_private_key=request.ssh_private_key,
            ssh_passphrase=request.ssh_passphrase,
            validate_on_create=request.validate_on_create
        )
        return ProxyResponse.from_orm(proxy)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{proxy_id}", response_model=ProxyResponse)
async def update_proxy(proxy_id: int, request: ProxyUpdateRequest):
    """Update proxy"""
    try:
        # Convert request to dict, excluding None values
        updates = {k: v for k, v in request.dict().items() if v is not None}
        
        proxy = await proxy_service.update_proxy(proxy_id, **updates)
        if not proxy:
            raise HTTPException(status_code=404, detail="Proxy not found")
        
        return ProxyResponse.from_orm(proxy)
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{proxy_id}")
async def delete_proxy(proxy_id: int):
    """Delete proxy"""
    try:
        success = await proxy_service.delete_proxy(proxy_id)
        if not success:
            raise HTTPException(status_code=404, detail="Proxy not found")
        return {"message": "Proxy deleted successfully"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{proxy_id}/validate")
async def validate_proxy(proxy_id: int):
    """Validate proxy connectivity"""
    try:
        result = await proxy_service.validate_proxy(proxy_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/validate-all")
async def validate_all_proxies():
    """Validate all proxies"""
    try:
        result = await proxy_service.validate_all_proxies()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics/summary")
async def get_proxy_statistics():
    """Get proxy statistics"""
    try:
        stats = await proxy_service.get_proxy_statistics()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/rotate")
async def rotate_proxy(current_proxy_id: Optional[int] = Body(None, embed=True)):
    """Get next proxy in rotation"""
    try:
        proxy = await proxy_service.rotate_proxy(current_proxy_id)
        if not proxy:
            raise HTTPException(status_code=404, detail="No active proxies available")
        return ProxyResponse.from_orm(proxy)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{proxy_id}/test")
async def test_proxy_with_url(
    proxy_id: int,
    test_url: str = Body("https://httpbin.org/ip", embed=True)
):
    """Test proxy with specific URL"""
    try:
        result = await proxy_service.test_proxy_with_url(proxy_id, test_url)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/import")
async def import_proxies(request: ProxyImportRequest):
    """Import multiple proxies from list"""
    try:
        result = await proxy_service.import_proxies_from_list(
            proxy_list=request.proxies,
            validate_on_import=request.validate_on_import
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

"""
TikTok Account management API endpoints
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Query, Body
from pydantic import BaseModel, Field

from services.account_service import AccountService
from services.cookie_service import CookieService
from models.tiktok_account import TikTokAccount

router = APIRouter()
account_service = AccountService()
cookie_service = CookieService()


# Pydantic models for request/response
class AccountCreateRequest(BaseModel):
    username: str = Field(..., min_length=1, max_length=255)
    display_name: Optional[str] = Field(None, max_length=255)
    email: Optional[str] = Field(None, max_length=255)
    phone: Optional[str] = Field(None, max_length=50)
    browser_profile_id: Optional[int] = None
    notes: Optional[str] = Field(None, max_length=1000)
    tags: Optional[List[str]] = None


class AccountUpdateRequest(BaseModel):
    username: Optional[str] = Field(None, min_length=1, max_length=255)
    display_name: Optional[str] = Field(None, max_length=255)
    email: Optional[str] = Field(None, max_length=255)
    phone: Optional[str] = Field(None, max_length=50)
    browser_profile_id: Optional[int] = None
    is_active: Optional[bool] = None
    notes: Optional[str] = Field(None, max_length=1000)
    tags: Optional[List[str]] = None


class AccountResponse(BaseModel):
    id: int
    username: str
    display_name: Optional[str]
    email: Optional[str]
    phone: Optional[str]
    is_active: bool
    is_logged_in: bool
    is_verified: bool
    is_banned: bool
    profile_picture_url: Optional[str]
    bio: Optional[str]
    follower_count: int
    following_count: int
    likes_count: int
    videos_count: int
    browser_profile_id: Optional[int]
    last_login: Optional[str]
    login_count: int
    failed_login_attempts: int
    daily_activity: Dict[str, int]
    engagement_rate: float
    warning_count: int
    last_warning: Optional[str]
    warning_message: Optional[str]
    notes: Optional[str]
    tags: List[str]
    created_at: str
    updated_at: str

    class Config:
        from_attributes = True


class LoginRequest(BaseModel):
    manual_login: bool = True
    save_cookies: bool = True


@router.get("/", response_model=List[AccountResponse])
async def get_accounts(
    active_only: bool = Query(False, description="Filter active accounts only"),
    logged_in_only: bool = Query(False, description="Filter logged in accounts only"),
    limit: Optional[int] = Query(None, ge=1, le=100, description="Limit number of results"),
    offset: int = Query(0, ge=0, description="Offset for pagination")
):
    """Get list of TikTok accounts"""
    try:
        accounts = await account_service.get_accounts(
            active_only=active_only,
            logged_in_only=logged_in_only,
            limit=limit,
            offset=offset
        )
        return [AccountResponse.from_orm(account) for account in accounts]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{account_id}", response_model=AccountResponse)
async def get_account(account_id: int):
    """Get account by ID"""
    try:
        account = await account_service.get_account(account_id)
        if not account:
            raise HTTPException(status_code=404, detail="Account not found")
        return AccountResponse.from_orm(account)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/", response_model=AccountResponse)
async def create_account(request: AccountCreateRequest):
    """Create a new TikTok account"""
    try:
        account = await account_service.create_account(
            username=request.username,
            display_name=request.display_name,
            email=request.email,
            phone=request.phone,
            browser_profile_id=request.browser_profile_id,
            notes=request.notes,
            tags=request.tags
        )
        return AccountResponse.from_orm(account)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{account_id}", response_model=AccountResponse)
async def update_account(account_id: int, request: AccountUpdateRequest):
    """Update account"""
    try:
        # Convert request to dict, excluding None values
        updates = {k: v for k, v in request.dict().items() if v is not None}

        account = await account_service.update_account(account_id, **updates)
        if not account:
            raise HTTPException(status_code=404, detail="Account not found")

        return AccountResponse.from_orm(account)
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{account_id}")
async def delete_account(account_id: int):
    """Delete account"""
    try:
        success = await account_service.delete_account(account_id)
        if not success:
            raise HTTPException(status_code=404, detail="Account not found")
        return {"message": "Account deleted successfully"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{account_id}/login")
async def login_account(account_id: int, request: LoginRequest):
    """Login to TikTok account"""
    try:
        result = await account_service.login_account(
            account_id=account_id,
            manual_login=request.manual_login,
            save_cookies=request.save_cookies
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{account_id}/logout")
async def logout_account(account_id: int):
    """Logout from TikTok account"""
    try:
        result = await account_service.logout_account(account_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{account_id}/login-status")
async def check_login_status(account_id: int):
    """Check account login status"""
    try:
        result = await account_service.check_login_status(account_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{account_id}/refresh")
async def refresh_account_data(account_id: int):
    """Refresh account data from TikTok"""
    try:
        result = await account_service.refresh_account_data(account_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics/summary")
async def get_account_statistics():
    """Get account statistics"""
    try:
        stats = await account_service.get_account_statistics()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Cookie management endpoints
@router.get("/{account_id}/cookies")
async def get_account_cookies(account_id: int):
    """Get account cookies (for debugging)"""
    try:
        cookies = await cookie_service.load_cookies(account_id)
        if not cookies:
            raise HTTPException(status_code=404, detail="No cookies found")

        # Return cookie count and domains for security
        cookie_info = {
            "count": len(cookies),
            "domains": list(set(cookie.get("domain", "") for cookie in cookies)),
            "has_session": any("session" in cookie.get("name", "").lower() for cookie in cookies)
        }
        return cookie_info
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{account_id}/cookies")
async def delete_account_cookies(account_id: int):
    """Delete account cookies"""
    try:
        success = await cookie_service.delete_cookies(account_id)
        if not success:
            raise HTTPException(status_code=404, detail="Account not found")
        return {"message": "Cookies deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{account_id}/cookies/validate")
async def validate_account_cookies(account_id: int):
    """Validate account cookies"""
    try:
        result = await cookie_service.validate_cookies(account_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{account_id}/cookies/export")
async def export_account_cookies(
    account_id: int,
    format: str = Body("json", embed=True)
):
    """Export account cookies"""
    try:
        if format not in ["json", "netscape"]:
            raise HTTPException(status_code=400, detail="Format must be 'json' or 'netscape'")

        cookies_data = await cookie_service.export_cookies(account_id, format)
        if not cookies_data:
            raise HTTPException(status_code=404, detail="No cookies found")

        return {
            "format": format,
            "data": cookies_data,
            "filename": f"account_{account_id}_cookies.{format}"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{account_id}/cookies/import")
async def import_account_cookies(
    account_id: int,
    cookies_data: str = Body(..., embed=True),
    format: str = Body("json", embed=True)
):
    """Import account cookies"""
    try:
        if format not in ["json", "netscape"]:
            raise HTTPException(status_code=400, detail="Format must be 'json' or 'netscape'")

        success = await cookie_service.import_cookies(account_id, cookies_data, format)
        if not success:
            raise HTTPException(status_code=400, detail="Failed to import cookies")

        return {"message": "Cookies imported successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/cookies/cleanup")
async def cleanup_expired_cookies():
    """Cleanup expired cookies from all accounts"""
    try:
        result = await cookie_service.cleanup_expired_cookies()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

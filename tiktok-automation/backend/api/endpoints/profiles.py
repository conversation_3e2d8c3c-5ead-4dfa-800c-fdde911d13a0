"""
Profile management API endpoints
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from fastapi import APIRouter, HTTPException, Query, Body, Depends
from pydantic import BaseModel, Field, field_validator
from sqlalchemy.ext.asyncio import AsyncSession

from services.profile_service import ProfileService
# from models.browser_profile import BrowserProfile  # Commented out to avoid greenlet issues
from core.database import get_async_session

router = APIRouter()

async def get_profile_service(session: AsyncSession = Depends(get_async_session)) -> ProfileService:
    return ProfileService(session)


# Pydantic models for request/response
class ProfileCreateRequest(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    proxy_id: Optional[int] = None
    fingerprint_config: Optional[Dict[str, Any]] = None
    auto_generate_fingerprint: bool = True
    os_preference: str = Field("windows", pattern="^(windows|macos|linux)$")
    browser_preference: str = Field("firefox", pattern="^(firefox|chrome)$")


class ProfileUpdateRequest(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    proxy_id: Optional[int] = None
    is_active: Optional[bool] = None
    fingerprint_config: Optional[Dict[str, Any]] = None
    user_agent: Optional[str] = None
    timezone: Optional[str] = None
    locale: Optional[str] = None


class ProfileDuplicateRequest(BaseModel):
    new_name: str = Field(..., min_length=1, max_length=255)
    regenerate_fingerprint: bool = False


class ProfileResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    is_active: bool
    proxy_id: Optional[int]
    proxy_info: Optional[Dict[str, Any]] = None  # Will be populated manually
    user_agent: Optional[str]
    timezone: Optional[str]
    locale: Optional[str]
    usage_count: int
    last_used: Optional[str]
    created_at: str
    updated_at: str

    @field_validator('created_at', 'updated_at', 'last_used', mode='before')
    @classmethod
    def serialize_datetime(cls, v):
        if isinstance(v, datetime):
            return v.isoformat()
        return v

    @classmethod
    def from_profile(cls, profile, proxy_info: Optional[Dict[str, Any]] = None):
        """Create ProfileResponse from BrowserProfile with safe proxy_info"""
        return cls(
            id=profile.id,
            name=profile.name,
            description=profile.description,
            is_active=profile.is_active,
            proxy_id=profile.proxy_id,
            proxy_info=proxy_info,
            user_agent=profile.user_agent,
            timezone=profile.timezone,
            locale=profile.locale,
            usage_count=profile.usage_count,
            last_used=profile.last_used.isoformat() if profile.last_used else None,
            created_at=profile.created_at.isoformat(),
            updated_at=profile.updated_at.isoformat()
        )

    class Config:
        from_attributes = True


@router.get("/", response_model=List[ProfileResponse])
async def get_profiles(
    active_only: bool = Query(False, description="Filter active profiles only"),
    limit: Optional[int] = Query(None, ge=1, le=100, description="Limit number of results"),
    offset: int = Query(0, ge=0, description="Offset for pagination"),
    service: ProfileService = Depends(get_profile_service)
):
    """Get list of browser profiles"""
    try:
        profiles = await service.get_profiles(
            active_only=active_only,
            limit=limit,
            offset=offset
        )

        # Build response without proxy_info to avoid lazy loading
        result = []
        for profile in profiles:
            response = ProfileResponse.from_orm(profile)
            response.proxy_info = None  # Avoid lazy loading issues
            result.append(response)

        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{profile_id}", response_model=ProfileResponse)
async def get_profile(
    profile_id: int,
    service: ProfileService = Depends(get_profile_service)
):
    """Get profile by ID"""
    try:
        profile = await service.get_profile(profile_id)
        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")
        return ProfileResponse.from_orm(profile)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/", response_model=ProfileResponse)
async def create_profile(
    request: ProfileCreateRequest,
    service: ProfileService = Depends(get_profile_service)
):
    """Create a new browser profile"""
    try:
        profile = await service.create_profile(
            name=request.name,
            description=request.description,
            proxy_id=request.proxy_id,
            fingerprint_config=request.fingerprint_config,
            auto_generate_fingerprint=request.auto_generate_fingerprint,
            os_preference=request.os_preference,
            browser_preference=request.browser_preference
        )
        return ProfileResponse.from_orm(profile)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{profile_id}", response_model=ProfileResponse)
async def update_profile(
    profile_id: int,
    request: ProfileUpdateRequest,
    service: ProfileService = Depends(get_profile_service)
):
    """Update profile"""
    try:
        # Convert request to dict, excluding None values
        updates = {k: v for k, v in request.dict().items() if v is not None}

        profile = await service.update_profile(profile_id, **updates)
        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")
        
        return ProfileResponse.from_orm(profile)
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{profile_id}")
async def delete_profile(profile_id: int):
    """Delete profile using direct SQLite operations to avoid greenlet issues"""
    try:
        import sqlite3
        import os

        # Get database path
        db_path = "./tiktok_automation.db"
        if not os.path.exists(db_path):
            raise HTTPException(status_code=500, detail="Database file not found")

        # Use direct SQLite connection
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        try:
            # Check if profile exists
            cursor.execute("SELECT id FROM browser_profiles WHERE id = ?", (profile_id,))
            if not cursor.fetchone():
                raise HTTPException(status_code=404, detail="Profile not found")

            # Check if profile has associated accounts
            cursor.execute("SELECT COUNT(*) FROM tiktok_accounts WHERE browser_profile_id = ?", (profile_id,))
            account_count = cursor.fetchone()[0]

            if account_count > 0:
                raise HTTPException(status_code=400, detail="Cannot delete profile that has associated TikTok accounts")

            # Delete profile
            cursor.execute("DELETE FROM browser_profiles WHERE id = ?", (profile_id,))
            conn.commit()

            return {"message": "Profile deleted successfully"}

        finally:
            conn.close()

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))





@router.post("/{profile_id}/duplicate", response_model=ProfileResponse)
async def duplicate_profile(
    profile_id: int,
    request: ProfileDuplicateRequest,
    service: ProfileService = Depends(get_profile_service)
):
    """Duplicate an existing profile"""
    try:
        profile = await service.duplicate_profile(
            profile_id=profile_id,
            new_name=request.new_name,
            regenerate_fingerprint=request.regenerate_fingerprint
        )
        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")

        return ProfileResponse.from_orm(profile)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{profile_id}/test")
async def test_profile(
    profile_id: int,
    service: ProfileService = Depends(get_profile_service)
):
    """Test profile by launching browser"""
    try:
        result = await service.test_profile(profile_id)
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{profile_id}/login")
async def login_profile(
    profile_id: int,
    service: ProfileService = Depends(get_profile_service)
):
    """Start login process for profile"""
    try:
        result = await service.start_login(profile_id)
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{profile_id}/complete-login")
async def complete_login_profile(
    profile_id: int,
    service: ProfileService = Depends(get_profile_service)
):
    """Complete login process for profile - mark as ready"""
    try:
        result = await service.complete_login(profile_id)
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/templates/list")
async def get_profile_templates(
    service: ProfileService = Depends(get_profile_service)
):
    """Get available profile templates"""
    try:
        templates = await service.get_profile_templates()
        return {"templates": templates}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/templates/{template_name}", response_model=ProfileResponse)
async def create_from_template(
    template_name: str,
    profile_name: str = Body(..., embed=True),
    proxy_id: Optional[int] = Body(None, embed=True),
    service: ProfileService = Depends(get_profile_service)
):
    """Create profile from template"""
    try:
        profile = await service.create_from_template(
            template_name=template_name,
            profile_name=profile_name,
            proxy_id=proxy_id
        )
        if not profile:
            raise HTTPException(status_code=404, detail="Template not found")

        return ProfileResponse.from_orm(profile)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
